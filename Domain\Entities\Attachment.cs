﻿using Domain.Common;

namespace Domain.Entities;

public sealed class Attachment : BaseEntity
{
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? Extension { get; set; }
    public string? Path { get; set; }
    public string? ContentType { get; set; }
    public string? Size { get; set; }
    public AttachmentHeader? AttachmentHeader { get; set; }
    public Guid? AttachmentHeaderId { get; set; }
}
