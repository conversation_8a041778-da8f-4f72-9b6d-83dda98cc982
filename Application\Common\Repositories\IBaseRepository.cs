﻿using Application.Common.Models;
using Domain.Common;

namespace Application.Common.Repositories;

public interface IBaseRepository<T> where T : BaseEntity
{
    T Create(T entity);
    Task<bool> IsExist(Guid Id);
    Task<T> GetByIdAsync(Guid Id, CancellationToken cancellationToken);
    T Update(T entity);
    Task<List<T>> GetAllAsync(CancellationToken cancellationToken);
    Task<List<T>> GetAllAsync(IFilter<T> filter, CancellationToken cancellationToken);
    Task<List<T>> GetAllActiveAsync(CancellationToken cancellationToken);
    Task<PagingList<T>> GetPaginatedAsync(int page, int pageSize, CancellationToken cancellationToken);
    Task<PagingList<T>> GetPaginatedActiveAsync(int page, int pageSize, CancellationToken cancellationToken);
    Task<PagingList<T>> GetPaginatedAsync(int page, int pageSize, IFilter<T>? filter, CancellationToken cancellationToken);
    Task<PagingList<T>> GetPaginatedAsync(int page, int pageSize, IFilter<T>? filter, ISort<T>? sorter, CancellationToken cancellationToken);
    Task<T?> GetDefaultItemByIdAsync(Guid Id, CancellationToken cancellationToken);

}