﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="FFMpegCore" Version="5.1.0" />
		<PackageReference Include="FluentValidation" Version="11.9.2" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.2" />
		<PackageReference Include="MediatR" Version="12.3.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Identity.Core" Version="8.0.8" />
		<PackageReference Include="QRCoder" Version="1.6.0" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
		<PackageReference Include="Xabe.FFmpeg" Version="5.2.6" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Domain\Domain.csproj" />
	</ItemGroup>

</Project>
