﻿using Application.Common;
using Application.Common.Models;
using Application.Common.Repositories;
using Domain.Common;
using Microsoft.EntityFrameworkCore;
using Persistence.Context;

namespace Persistence.Common;

public class BaseRepository<T> : IBaseRepository<T> where T : BaseEntity
{
    protected readonly AppDbContext Context;

    public BaseRepository(AppDbContext context)
    {
        Context = context;
    }

    public virtual T Create(T entity)
    {
        //entity.CreatedAt = DateTime.Now;
        Context.Add(entity);
        return entity;
    }
    public virtual async Task<bool> IsExist(Guid id)
    {
        var entity = await Context.Set<T>().FirstOrDefaultAsync(t => t.Id == id);
        bool exists = entity is not null;
        return exists;
    }

    public virtual T Update(T entity)
    {
        entity.UpdatedAt = DateTime.Now;
        entity.IsActive = entity.IsActive;
        entity.CreatedAt = entity.CreatedAt;
        entity.CreatedBy = entity.CreatedBy;
        entity.UpdatedAt = DateTime.Now;
        Context.Entry(entity).State = EntityState.Detached;
        Context.Update(entity);
        return entity;
    }

    public virtual Task<List<T>> GetAllAsync(CancellationToken cancellationToken)
    {
        return Context.Set<T>()
            .Where(t => t.IsDeleted == false)
            .ToListAsync(cancellationToken);
    }

    public virtual Task<List<T>> GetAllAsync(IFilter<T> filter, CancellationToken cancellationToken)
    {
        IQueryable<T> query = Context.Set<T>().Where(x => !x.IsDeleted);

        if (filter != null)
            query = filter.Filter(query);

        return query
            .AsNoTracking()
            .ToListAsync(cancellationToken);
    }

    public virtual Task<List<T>> GetAllActiveAsync(CancellationToken cancellationToken)
    {
        return Context.Set<T>()
            .Where(t => t.IsDeleted == false && t.IsActive == true)
            .ToListAsync(cancellationToken);
    }

    public virtual async Task<T> GetByIdAsync(Guid Id, CancellationToken cancellationToken)
    {
        return await Context.Set<T>()
            .Where(t => t.IsDeleted == false)
            .AsNoTracking()
            .FirstAsync(t => t.Id == Id, cancellationToken);
    }

    public virtual async Task<PagingList<T>> GetPaginatedAsync(int page, int pageSize, CancellationToken cancellationToken)
    {
        var query = Context.Set<T>().Where(x => x.IsDeleted == false).OrderByDescending(a => a.CreatedAt);

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var count = await query.CountAsync(cancellationToken);

        var pageInfo = new PagingInfo
        {
            CurrentPage = page,
            PageSize = pageSize,
            TotalResults = count,
            TotalPages = PagingHelpers.GetTotalPages(count, pageSize)
        };

        var pagingList = new PagingList<T>(items, pageInfo);

        return pagingList;
    }

    public virtual async Task<PagingList<T>> GetPaginatedActiveAsync(int page, int pageSize, CancellationToken cancellationToken)
    {
        var query = Context.Set<T>().Where(x => x.IsDeleted == false && x.IsActive == true).OrderByDescending(a => a.CreatedAt);

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        var count = await query.CountAsync(cancellationToken);

        var pageInfo = new PagingInfo
        {
            CurrentPage = page,
            PageSize = pageSize,
            TotalResults = count,
            TotalPages = PagingHelpers.GetTotalPages(count, pageSize)
        };

        var pagingList = new PagingList<T>(items, pageInfo);

        return pagingList;
    }

    public virtual async Task<PagingList<T>> GetPaginatedAsync(int page, int pageSize, IFilter<T>? filter, CancellationToken cancellationToken)
    {
        // Validate page and page size
        if (page <= 0)
            throw new ArgumentOutOfRangeException(nameof(page), "Page number must be greater than 0.");

        if (pageSize <= 0)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "Page size must be greater than 0.");

        // Start with the base query
        IQueryable<T> query = Context.Set<T>().Where(x => !x.IsDeleted);

        // Apply filter if provided
        if (filter != null)
            query = filter.Filter(query);

        // Get total count for pagination info
        var totalCount = await query.CountAsync(cancellationToken);

        // Calculate pagination
        var result = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // Prepare paging info
        var pageInfo = new PagingInfo
        {
            CurrentPage = page,
            PageSize = pageSize,
            TotalResults = totalCount,
            TotalPages = PagingHelpers.GetTotalPages(totalCount, pageSize)
        };

        return new PagingList<T>(result, pageInfo);
    }

    public virtual async Task<PagingList<T>> GetPaginatedAsync(int page, int pageSize, IFilter<T>? filter, ISort<T>? sorter, CancellationToken cancellationToken)
    {
        // Validate page and page size
        if (page <= 0)
            throw new ArgumentOutOfRangeException(nameof(page), "Page number must be greater than 0.");

        if (pageSize <= 0)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "Page size must be greater than 0.");

        // Start with the base query
        IQueryable<T> query = Context.Set<T>().Where(x => !x.IsDeleted);

        // Apply filter if provided
        if (filter != null)
            query = filter.Filter(query);

        // Get total count for pagination info
        var totalCount = await query.CountAsync(cancellationToken);

        // Apply sorting if provided
        IOrderedQueryable<T>? sortedQuery = sorter != null ? sorter.Sort(query) : query.OrderByDescending(x => x.CreatedAt); // Default sorting by CreatedAt

        // Calculate pagination
        var result = await sortedQuery
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .AsNoTracking()
            .ToListAsync(cancellationToken);

        // Prepare paging info
        var pageInfo = new PagingInfo
        {
            CurrentPage = page,
            PageSize = pageSize,
            TotalResults = totalCount,
            TotalPages = PagingHelpers.GetTotalPages(totalCount, pageSize)
        };

        return new PagingList<T>(result, pageInfo);
    }

    public virtual async Task<T?> GetDefaultItemByIdAsync(Guid Id, CancellationToken cancellationToken)
    {
        return await Context.Set<T>()
            .FirstOrDefaultAsync(t => t.Id == Id, cancellationToken);
    }
}