﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Presentation.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ErrorMessages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ErrorMessages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Presentation.Resources.ErrorMessages", typeof(ErrorMessages).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t activate this address.
        /// </summary>
        public static string _0101 {
            get {
                return ResourceManager.GetString("0101", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t create this address.
        /// </summary>
        public static string _0102 {
            get {
                return ResourceManager.GetString("0102", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Not Found.
        /// </summary>
        public static string _0103 {
            get {
                return ResourceManager.GetString("0103", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t deactivate address.
        /// </summary>
        public static string _0104 {
            get {
                return ResourceManager.GetString("0104", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error while returning addresses.
        /// </summary>
        public static string _0105 {
            get {
                return ResourceManager.GetString("0105", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error while updating address.
        /// </summary>
        public static string _0106 {
            get {
                return ResourceManager.GetString("0106", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t create.
        /// </summary>
        public static string _101 {
            get {
                return ResourceManager.GetString("101", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prove Not Found.
        /// </summary>
        public static string _1101 {
            get {
                return ResourceManager.GetString("1101", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prove Shared User Is Not Found.
        /// </summary>
        public static string _1301 {
            get {
                return ResourceManager.GetString("1301", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Token Is Expired.
        /// </summary>
        public static string _1302 {
            get {
                return ResourceManager.GetString("1302", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Already Sent.
        /// </summary>
        public static string _201 {
            get {
                return ResourceManager.GetString("201", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile Number Is Not Exist.
        /// </summary>
        public static string _202 {
            get {
                return ResourceManager.GetString("202", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Exception.
        /// </summary>
        public static string _203 {
            get {
                return ResourceManager.GetString("203", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Is Not Found.
        /// </summary>
        public static string _204 {
            get {
                return ResourceManager.GetString("204", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you don&apos;t have privilege for this feature.
        /// </summary>
        public static string _205 {
            get {
                return ResourceManager.GetString("205", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected address not found.
        /// </summary>
        public static string address_not_found {
            get {
                return ResourceManager.GetString("address_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected city not found.
        /// </summary>
        public static string city_not_found {
            get {
                return ResourceManager.GetString("city_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected corporate not found.
        /// </summary>
        public static string corporate_not_found {
            get {
                return ResourceManager.GetString("corporate_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected corporate not related to this prove.
        /// </summary>
        public static string corporate_prove_not_same {
            get {
                return ResourceManager.GetString("corporate_prove_not_same", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected corporate user request not found.
        /// </summary>
        public static string corporate_user_request_not_found {
            get {
                return ResourceManager.GetString("corporate_user_request_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generl error while accepting request.
        /// </summary>
        public static string general_accept {
            get {
                return ResourceManager.GetString("general_accept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while activate address.
        /// </summary>
        public static string general_activate_address {
            get {
                return ResourceManager.GetString("general_activate_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while activate city.
        /// </summary>
        public static string general_activate_city {
            get {
                return ResourceManager.GetString("general_activate_city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while activate prove.
        /// </summary>
        public static string general_activate_prove {
            get {
                return ResourceManager.GetString("general_activate_prove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while activate prove type.
        /// </summary>
        public static string general_activate_prove_type {
            get {
                return ResourceManager.GetString("general_activate_prove_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while Cancelling request.
        /// </summary>
        public static string general_Cancel {
            get {
                return ResourceManager.GetString("general_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while create address.
        /// </summary>
        public static string general_create_address {
            get {
                return ResourceManager.GetString("general_create_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while create city.
        /// </summary>
        public static string general_create_city {
            get {
                return ResourceManager.GetString("general_create_city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while create prove.
        /// </summary>
        public static string general_create_prove {
            get {
                return ResourceManager.GetString("general_create_prove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while create prove attachment.
        /// </summary>
        public static string general_create_prove_attachment {
            get {
                return ResourceManager.GetString("general_create_prove_attachment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while create prove type.
        /// </summary>
        public static string general_create_prove_type {
            get {
                return ResourceManager.GetString("general_create_prove_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while deactivate address.
        /// </summary>
        public static string general_deactivate_address {
            get {
                return ResourceManager.GetString("general_deactivate_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while deactivate city.
        /// </summary>
        public static string general_deactivate_city {
            get {
                return ResourceManager.GetString("general_deactivate_city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while deactivate prove.
        /// </summary>
        public static string general_deactivate_prove {
            get {
                return ResourceManager.GetString("general_deactivate_prove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while deactivate prove type.
        /// </summary>
        public static string general_deactivate_prove_type {
            get {
                return ResourceManager.GetString("general_deactivate_prove_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while delete address.
        /// </summary>
        public static string general_delete_address {
            get {
                return ResourceManager.GetString("general_delete_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while delete city.
        /// </summary>
        public static string general_delete_city {
            get {
                return ResourceManager.GetString("general_delete_city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while delete prove.
        /// </summary>
        public static string general_delete_prove {
            get {
                return ResourceManager.GetString("general_delete_prove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while delete prove attachment.
        /// </summary>
        public static string general_delete_prove_attachment {
            get {
                return ResourceManager.GetString("general_delete_prove_attachment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while delete prove type.
        /// </summary>
        public static string general_delete_prove_type {
            get {
                return ResourceManager.GetString("general_delete_prove_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while return address.
        /// </summary>
        public static string general_get_address {
            get {
                return ResourceManager.GetString("general_get_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while return cities.
        /// </summary>
        public static string general_get_all_cities {
            get {
                return ResourceManager.GetString("general_get_all_cities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while returning prove types.
        /// </summary>
        public static string general_get_all_prove_types {
            get {
                return ResourceManager.GetString("general_get_all_prove_types", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while return city.
        /// </summary>
        public static string general_get_city {
            get {
                return ResourceManager.GetString("general_get_city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while return addresses.
        /// </summary>
        public static string general_get_paginated_address {
            get {
                return ResourceManager.GetString("general_get_paginated_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while returning proves.
        /// </summary>
        public static string general_get_paginated_prove {
            get {
                return ResourceManager.GetString("general_get_paginated_prove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while return prove.
        /// </summary>
        public static string general_get_prove_by_id {
            get {
                return ResourceManager.GetString("general_get_prove_by_id", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while return prove type.
        /// </summary>
        public static string general_get_prove_type {
            get {
                return ResourceManager.GetString("general_get_prove_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unhandled exception occurred while trying to process your request, please try again later.
        /// </summary>
        public static string general_unhandled_exception {
            get {
                return ResourceManager.GetString("general_unhandled_exception", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while update address.
        /// </summary>
        public static string general_update_address {
            get {
                return ResourceManager.GetString("general_update_address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while update city.
        /// </summary>
        public static string general_update_city {
            get {
                return ResourceManager.GetString("general_update_city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while update prove.
        /// </summary>
        public static string general_update_prove {
            get {
                return ResourceManager.GetString("general_update_prove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while update prove attachment.
        /// </summary>
        public static string general_update_prove_attachment {
            get {
                return ResourceManager.GetString("general_update_prove_attachment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General error while update prove type.
        /// </summary>
        public static string general_update_prove_type {
            get {
                return ResourceManager.GetString("general_update_prove_type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Current Password.
        /// </summary>
        public static string invalid_current_password {
            get {
                return ResourceManager.GetString("invalid_current_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The provided OTP is invalid.
        /// </summary>
        public static string invalid_otp {
            get {
                return ResourceManager.GetString("invalid_otp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The provided token is invalid.
        /// </summary>
        public static string invalid_token {
            get {
                return ResourceManager.GetString("invalid_token", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The username or password provided is invalid.
        /// </summary>
        public static string invalid_username_or_password {
            get {
                return ResourceManager.GetString("invalid_username_or_password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User doesn&apos;t have permission to access this prove.
        /// </summary>
        public static string not_authorized_user {
            get {
                return ResourceManager.GetString("not_authorized_user", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You don&apos;t have privilege to this feature.
        /// </summary>
        public static string Privilege_cancel {
            get {
                return ResourceManager.GetString("Privilege_cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected prove attachment not found.
        /// </summary>
        public static string prove_attachment_not_found {
            get {
                return ResourceManager.GetString("prove_attachment_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected prove not found.
        /// </summary>
        public static string prove_not_found {
            get {
                return ResourceManager.GetString("prove_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prove shared user not found.
        /// </summary>
        public static string prove_shared_user_not_found {
            get {
                return ResourceManager.GetString("prove_shared_user_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected prove type not found.
        /// </summary>
        public static string prove_type_not_found {
            get {
                return ResourceManager.GetString("prove_type_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Already Sent.
        /// </summary>
        public static string Request_already_sent {
            get {
                return ResourceManager.GetString("Request_already_sent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The request could not be found.
        /// </summary>
        public static string request_not_found {
            get {
                return ResourceManager.GetString("request_not_found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Not Found.
        /// </summary>
        public static string Request_Not_Found_accept {
            get {
                return ResourceManager.GetString("Request_Not_Found_accept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The request has not been verified.
        /// </summary>
        public static string request_not_verified {
            get {
                return ResourceManager.GetString("request_not_verified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The token has already been used.
        /// </summary>
        public static string token_already_used {
            get {
                return ResourceManager.GetString("token_already_used", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The user is already registered.
        /// </summary>
        public static string user_already_registered {
            get {
                return ResourceManager.GetString("user_already_registered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected user not found .
        /// </summary>
        public static string user_not_found {
            get {
                return ResourceManager.GetString("user_not_found", resourceCulture);
            }
        }
    }
}
