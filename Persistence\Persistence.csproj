﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FFMpegCore" Version="5.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Npgsql" Version="8.0.5" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.10" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.Design" Version="1.1.0" />
		<PackageReference Include="NReco.VideoConverter" Version="1.2.1" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
	</ItemGroup>



	<ItemGroup>
	  <Folder Include="Migration\" />
	</ItemGroup>



	<ItemGroup>
	  <ProjectReference Include="..\Application\Application.csproj" />
	  <ProjectReference Include="..\Domain\Domain.csproj" />
	</ItemGroup>

</Project>
