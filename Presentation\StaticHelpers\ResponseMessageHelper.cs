﻿using System.Resources;

namespace Presentation.StaticHelpers
{
    public static class ResponseMessageHelperInstance
    {
        private static ResourceReader businessErrosAr = new ResourceReader("Resources/BusinessErrors/BusinessErrors_ar.resx");
        public static ResourceReader businessErrorsEn = new ResourceReader("Resources/BusinessErrors/BusinessErrors_en.resx");

        public static ResourceSet successMessagesEn = new ResourceSet("Resources/SuccessMessages/SuccessMessages_ar.resx");
        public static ResourceSet successMessagesAr = new ResourceSet("Resources/SuccessMessages/SuccessMessages_en.resx");

        public static ResourceSet validationErrorsAr = new ResourceSet("Resources/ValidationsErrors/ValidationErrors_ar.resx");
        public static ResourceSet validationErrorsEn = new ResourceSet("Resources/ValidationsErrors/ValidationErrors_en.resx");

        public static ResourceSet entitiesEn = new ResourceSet("Resources/EntitiesNames/EntitiesNames_ar.resx");
        public static ResourceSet entitiesAr = new ResourceSet("Resources/EntitiesNames/EntitiesNames_en.resx");

        public static Dictionary<string, string> OperationsCodes = new Dictionary<string, string>(){
            {"create", "101"},
            {"update", "102"},
            {"delete", "103"}
        };
        public static string FirstCharToUpper(this string input) =>
            input switch
            {
                null => throw new ArgumentNullException(nameof(input)),
                "" => throw new ArgumentException($"{nameof(input)} cannot be empty", nameof(input)),
                _ => input[0].ToString().ToUpper() + input.Substring(1)
            };

    }
}
