﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="101" xml:space="preserve">
    <value>لا يمكن إضافة</value>
  </data>
  <data name="201" xml:space="preserve">
    <value>تم إرسال الطلب من قبل</value>
  </data>
  <data name="204" xml:space="preserve">
    <value>الطلب غير موجود</value>
  </data>
  <data name="205" xml:space="preserve">
    <value>ليس لديك امتياز لهذه الميزة</value>
  </data>
  <data name="202" xml:space="preserve">
    <value>رقم الجوال غير موجود في النظام</value>
  </data>
  <data name="203" xml:space="preserve">
    <value>خطأ عام </value>
  </data>
  <data name="0101" xml:space="preserve">
    <value>لا يمكن تفعيل هذا العنوان</value>
  </data>
  <data name="0102" xml:space="preserve">
    <value>لا يمكن اضافة هذا العنوان</value>
  </data>
  <data name="0103" xml:space="preserve">
    <value>لا يوجد هذا العنوان</value>
  </data>
  <data name="0104" xml:space="preserve">
    <value>خطأ اثناء الغاء تفعيل هذا العنوان</value>
  </data>
  <data name="0105" xml:space="preserve">
    <value>خطأ اثناء استرجاع العناوين</value>
  </data>
  <data name="0106" xml:space="preserve">
    <value>خطأ اثناء تعديل العنوان</value>
  </data>
  <data name="1101" xml:space="preserve">
    <value>لا يوجد هذا الأثبات</value>
  </data>
  <data name="general_activate_prove" xml:space="preserve">
    <value>خطأ عام  اثناء تفعيل الأثبات</value>
  </data>
  <data name="general_create_prove" xml:space="preserve">
    <value>خطأ عام  اثناء انشاء الأثبات</value>
  </data>
  <data name="general_deactivate_prove" xml:space="preserve">
    <value>خطأ عام اثناء الغاء تفعيل الأثبات</value>
  </data>
  <data name="general_delete_prove" xml:space="preserve">
    <value>خطأ عام  اثناء مسح الأثبات</value>
  </data>
  <data name="general_get_paginated_prove" xml:space="preserve">
    <value>خطأ عام  اثناء استرجاع الأثباتات</value>
  </data>
  <data name="general_update_prove" xml:space="preserve">
    <value>خطأ عام  اثناء تعديل الأثبات</value>
  </data>
  <data name="1301" xml:space="preserve">
    <value>لم يتم العثور على إثبات المستخدم المشترك</value>
  </data>
  <data name="1302" xml:space="preserve">
    <value>انتهت صلاحية الرمز المميز</value>
  </data>
  <data name="address_not_found" xml:space="preserve">
    <value>تعثر الحصول على العنوان</value>
  </data>
  <data name="prove_not_found" xml:space="preserve">
    <value>تعثر الحصول على الاثبات المطلوب مشاركته</value>
  </data>
  <data name="prove_shared_user_not_found" xml:space="preserve">
    <value>تعثر الحصول على الاثبات المطلوب مشاركته</value>
  </data>
  <data name="prove_request_not_found" xml:space="preserve">
    <value>تعثر الحصول على طلب الإثبات المطلوب </value>
  </data>
  <data name="prove_sharing_expired" xml:space="preserve">
    <value>لقد انتهت فترة مشاركة الإثبات </value>
  </data>
  <data name="prove_sharing_exceeded_allowed_opens" xml:space="preserve">
    <value>الإثبات المشترك تجاوز عدد مرات الفتح المسموح بها</value>
  </data>
  <data name="corporate_not_found" xml:space="preserve">
    <value>تعثر الحصول على الشركة</value>
  </data>
  <data name="corporate_user_not_found" xml:space="preserve">
    <value>تعثر الحصول على موظف الشركة</value>
  </data>
  <data name="Same_Corporate_User" xml:space="preserve">
    <value>لقد أدخلت نفس معرف الموظف</value>
  </data>
  <data name="corporate_user_request_not_found" xml:space="preserve">
    <value>تعثر الحصول على الشركة</value>
  </data>
  <data name="general_activate_prove_type" xml:space="preserve">
    <value>خطأ عام اثناء تفعيل نوع الأثبات</value>
  </data>
  <data name="general_create_prove_attachment" xml:space="preserve">
    <value>خطأ عام اثناء انشاء ملف الأثبات</value>
  </data>
  <data name="general_create_prove_type" xml:space="preserve">
    <value>خطأ عام اثناء انشاء نوع الأثبات</value>
  </data>
  <data name="general_deactivate_prove_type" xml:space="preserve">
    <value>خطأ عام اثناء الغاء تفعيل نوع الأثبات</value>
  </data>
  <data name="general_delete_prove_attachment" xml:space="preserve">
    <value>خطأ عام اثناء مسح ملف الأثبات</value>
  </data>
  <data name="general_delete_prove_type" xml:space="preserve">
    <value>خطأ عام اثناء مسح نوع الأثبات</value>
  </data>
  <data name="general_get_all_prove_types" xml:space="preserve">
    <value>خطأ عام  اثناء استرجاع انواع الأثباتات</value>
  </data>
  <data name="general_get_prove_by_id" xml:space="preserve">
    <value>خطأ عام   اثناء استرجاع الأثبات</value>
  </data>
  <data name="general_get_prove_type" xml:space="preserve">
    <value>خطأ عام   اثناء استرجاع نوع الأثبات</value>
  </data>
  <data name="general_update_prove_attachment" xml:space="preserve">
    <value>خطأ عام اثناء تعديل ملف الأثبات</value>
  </data>
  <data name="general_update_prove_type" xml:space="preserve">
    <value>خطأ عام   اثناء تعديل نوع الأثبات</value>
  </data>
  <data name="prove_attachment_not_found" xml:space="preserve">
    <value>تعثر الحصول على الملفات الخاصة بهذا الأثبات</value>
  </data>
  <data name="prove_type_not_found" xml:space="preserve">
    <value>تعثر الحصول على نوع الأثبات</value>
  </data>
  <data name="user_not_found" xml:space="preserve">
    <value>تعثر الحصول على المستخدم</value>
  </data>
  <data name="cannot_share_with_yourself" xml:space="preserve">
    <value>لا يمكنك المشاركة مع نفسك</value>
  </data>
  <data name="cant_share_yourself" xml:space="preserve">
    <value>لا يمكنك إضافة شركتك</value>
  </data>
  <data name="offer_type_not_found" xml:space="preserve">
    <value>تعثر الحصول على نوع العرض</value>
  </data>
  <data name="failed_payment" xml:space="preserve">
    <value>المعاملة غير صحيحة</value>
  </data>
  <data name="corporate_not_subscribed" xml:space="preserve">
    <value>الشركة المستخدمة غير مشتركة في احدي الباقات</value>
  </data>
  <data name="resend_otp_not_allowed" xml:space="preserve">
    <value>إعادة إرسال رمز التحقق غير مسموح بها يرجى المحاولة مرة أخرى لاحقًا</value>
  </data>
  <data name="Mail_Exceptions" xml:space="preserve">
    <value>تعثر الحصول على المستخدم</value>
  </data>
  <data name="user_already_exists" xml:space="preserve">
    <value>المستخدم موجود بالفعل في الشركة</value>
  </data>
  <data name="request_already_sent" xml:space="preserve">
    <value>تم إرسال الطلب من قبل</value>
  </data>
  <data name="Cant_Send_To_Corporate" xml:space="preserve">
    <value>لا يمكن إرسال طلب لشركة</value>
  </data>
  <data name="invalid_username_or_password" xml:space="preserve">
    <value>اسم المستخدم أو كلمة المرور المقدمة غير صالحة</value>
  </data>
  <data name="request_not_found" xml:space="preserve">
    <value>لم يتم العثور على الطلب</value>
  </data>
  <data name="token_already_used" xml:space="preserve">
    <value>تم استخدام الرمز بالفعل</value>
  </data>
  <data name="invalid_otp" xml:space="preserve">
    <value>الرمز المُقدم غير صالح</value>
  </data>
  <data name="invalid_token" xml:space="preserve">
    <value>الرمز المُقدم غير صالح</value>
  </data>
  <data name="user_already_registered" xml:space="preserve">
    <value>المستخدم مسجل بالفعل</value>
  </data>
  <data name="request_not_verified" xml:space="preserve">
    <value>لم يتم التحقق من الطلب</value>
  </data>
  <data name="city_not_found" xml:space="preserve">
    <value>تعثر الحصول على المدينة</value>
  </data>
  <data name="general_activate_address" xml:space="preserve">
    <value>خطأ عام  اثناء تفعيل العنوان</value>
  </data>
  <data name="general_activate_city" xml:space="preserve">
    <value>خطأ عام  اثناء تفعيل المدينة</value>
  </data>
  <data name="general_create_address" xml:space="preserve">
    <value>خطأ عام  اثناء انشاء العنوان</value>
  </data>
  <data name="general_create_city" xml:space="preserve">
    <value>خطأ عام  اثناء انشاء المدينة</value>
  </data>
  <data name="general_deactivate_address" xml:space="preserve">
    <value>خطأ عام اثناء الغاء تفعيل العنوان</value>
  </data>
  <data name="general_deactivate_city" xml:space="preserve">
    <value>خطأ عام اثناء الغاء تفعيل المدينة</value>
  </data>
  <data name="general_delete_address" xml:space="preserve">
    <value>خطأ عام  اثناء مسح العنوان</value>
  </data>
  <data name="general_delete_city" xml:space="preserve">
    <value>خطأ عام  اثناء مسح المدينة</value>
  </data>
  <data name="general_get_address" xml:space="preserve">
    <value>خطأ عام  اثناء استرجاع العنوان</value>
  </data>
  <data name="general_get_all_cities" xml:space="preserve">
    <value>خطأ عام  اثناء استرجاع المدن</value>
  </data>
  <data name="general_get_city" xml:space="preserve">
    <value>خطأ عام  اثناء استرجاع المدينة</value>
  </data>
  <data name="general_get_paginated_address" xml:space="preserve">
    <value>خطأ عام  اثناء استرجاع العناوين</value>
  </data>
  <data name="general_update_address" xml:space="preserve">
    <value>خطأ عام  اثناء تعديل العنوان</value>
  </data>
  <data name="general_update_city" xml:space="preserve">
    <value>خطأ عام  اثناء تعديل المدينة</value>
  </data>
  <data name="corporate_prove_not_same" xml:space="preserve">
    <value>تعثر الحصول على الاثبات المطلوب داخل هذه الشركة</value>
  </data>
  <data name="not_authorized_user" xml:space="preserve">
    <value>المستخدم لا يملك الصلاحية لرؤية هذا الأثبات</value>
  </data>
  <data name="invalid_current_password" xml:space="preserve">
    <value>كلمة المرور المٌقدمة غير صحيحة</value>
  </data>
  <data name="general_unhandled_exception" xml:space="preserve">
    <value>حدث خطأ غير متوقع أثناء محاولة تنفيذ طلبك, نأمل المحاولة في وقت لاحق</value>
  </data>
  <data name="Request_Not_Found_accept" xml:space="preserve">
    <value>الطلب غير موجود</value>
  </data>
  <data name="Privilege_cancel" xml:space="preserve">
    <value>انت لا تملك صلاحيه لهذة الميزة</value>
  </data>
  <data name="Cannot_cancel" xml:space="preserve">
    <value>لا يمكن إلغاء طلب الإثبات</value>
  </data>
  <data name="Cannot_Complete" xml:space="preserve">
    <value>لا يمكن إستكمال طلب الإثبات</value>
  </data>
  <data name="Cannot_delete" xml:space="preserve">
    <value>لا يمكن حذف طلب الإثبات</value>
  </data>
  <data name="general_accept" xml:space="preserve">
    <value>خطأ عام أثتاء قبول الطلب</value>
  </data>
  <data name="general_Cancel" xml:space="preserve">
    <value>خطأ عام أثتاء إلغاء الطلب</value>
  </data>
  <data name="unauthorized_deletion" xml:space="preserve">
    <value>ليس لديك صلاحية لحذف المستخدم</value>
  </data>
  <data name="prove_already_shared_with_selected_user" xml:space="preserve">
    <value>الاثبات مشارك بالفعل مع المستخدم الذي تم اختياره</value>
  </data>
  <data name="prove_folder_already_shared_with_selected_user" xml:space="preserve">
    <value>ملف الاثبات مشارك بالفعل مع المستخدم الذي تم اختياره</value>
  </data>
  <data name="general_update_user" xml:space="preserve">
    <value>خطأ عام   اثناء تعديل المستخدم</value>
  </data>
  <data name="profile_picture_not_found" xml:space="preserve">
    <value>تعثر الحصول على الصورة الشخصية</value>
  </data>
  <data name="plan_not_found" xml:space="preserve">
    <value>تعثر الحصول على الكوته</value>
  </data>
  <data name="offer_not_found" xml:space="preserve">
    <value>تعثر الحصول على العرض</value>
  </data>
  <data name="prove_request_not_authorized" xml:space="preserve">
    <value>ليس لديك صلاحية إكمال طلب الإثبات المختار</value>
  </data>
  <data name="force_update_msg" xml:space="preserve">
    <value>للاستمتاع بالمزايا المضافة حديثاً في أثبت, نأمل تحديث التطبيق للنسخة الأخيرة</value>
  </data>
  <data name="cant-connect" xml:space="preserve">
    <value>لا يمكنك الاتصال بقاعدة البيانات</value>
  </data>
</root>