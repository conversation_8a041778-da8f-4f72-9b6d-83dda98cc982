﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>42e3d258-db04-4439-9b3e-939ab2c4a04b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Google.Cloud.Storage.V1" Version="4.11.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Localization" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.6" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Serilog" Version="4.0.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="8.0.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.6.2" />
		<PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.6.2" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.6.2" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\Application\Application.csproj" />
	  <ProjectReference Include="..\Persistence\Persistence.csproj" />
	</ItemGroup>


</Project>
