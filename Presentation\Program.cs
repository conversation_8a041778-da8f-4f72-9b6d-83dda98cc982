using Application;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Persistence;
using Presentation.Extensions;
using Presentation.Helpers;
using Presentation.Resources;
using Serilog;
using System.Text;
using System.Text.Json.Serialization;

namespace Presentation
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);
            ConfigurationManager configuration = builder.Configuration;

            // configure the web host to accept large request body
            builder.WebHost.ConfigureKestrel(severOptions =>
            {
                severOptions.Limits.MaxRequestBodySize = long.MaxValue;
            });

            //----- temporary inject the services till test the file upload
            //-----

            builder.Services.AddHttpContextAccessor();

            builder.Services.ConfigurePersistence(builder.Configuration);
            builder.Services.ConfigureApplication();

            builder.Services.AddControllers(options =>
            {
                options.Filters.Add(
                    new ResourceErrorFilter(
                        new MessageResourceContext(typeof(ErrorMessages)),
                        new MessageResourceContext(typeof(ValidationMessages)),
                        new MessageResourceContext(typeof(ValidationMembers))
                        )
                    );
            }).AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
                options.JsonSerializerOptions.WriteIndented = true;
            });

            builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
            string[] supportedCultures = { "en-US", "ar-EG" };
            var localizationOptions =
                new RequestLocalizationOptions().SetDefaultCulture(supportedCultures[0])
                .AddSupportedCultures(supportedCultures)
                .AddSupportedUICultures(supportedCultures);



            builder.Services.ConfigureApiBehavior();
            builder.Services.ConfigureCorsPolicy();

            builder.Host.UseSerilog((context, configuration) =>
                configuration.ReadFrom.Configuration(context.Configuration));

            //builder.Services.AddAuthentication(options =>
            //{
            //    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            //    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            //    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;

            //}).AddJwtBearer(options =>
            //{
            //    options.TokenValidationParameters = new TokenValidationParameters
            //    {
            //        ValidateIssuer = true,
            //        ValidateAudience = true,
            //        ValidateLifetime = true,
            //        ValidateIssuerSigningKey = true,
            //        ValidIssuer = builder.Configuration["Jwt:Issuer"],
            //        ValidAudience = builder.Configuration["Jwt:Audience"],
            //        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:SecretKey"]))
            //    };
            //});

            //builder.Services.ConfigureOptions<JwtOptionSetup>();
            //builder.Services.ConfigureOptions<AuthenticationOptionsSetup>();

            // todo: check why this is not working
            //builder.Services.ConfigureOptions<JwtBearerOptionsSetup>();

            //builder.Services.AddAuthorization();

            //var securityScheme = new OpenApiSecurityScheme()
            //{
            //    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
            //    Name = "Authorization",
            //    In = ParameterLocation.Header,
            //    Type = SecuritySchemeType.Http,
            //    Scheme = "bearer",
            //    BearerFormat = "JWT" // Optional
            //};

            builder.Services.AddControllers();
            builder.Services.AddEndpointsApiExplorer();
            //var securityRequirement = new OpenApiSecurityRequirement
            //{
            //    {
            //        new OpenApiSecurityScheme
            //        {
            //            Reference = new OpenApiReference
            //            {
            //                Type = ReferenceType.SecurityScheme,
            //                Id = "BearerAuthentication"
            //            }
            //        },
            //        new string[] {}
            //    }
            //};

            //builder.Services.AddSwaggerGen(options =>
            //{
            //    options.AddSecurityDefinition("BearerAuthentication", securityScheme);
            //    options.AddSecurityRequirement(securityRequirement);
            //});
            builder.Services.AddSwaggerGen();
            var app = builder.Build();

            //var serviceScope = app.Services.CreateScope();
            //var dataContext = serviceScope.ServiceProvider.GetService<DbContext>();
            //dataContext?.Database.EnsureCreated();

            app.UseSerilogRequestLogging();
            app.UseRequestLocalization(localizationOptions);
            app.UseDeveloperExceptionPage();
            app.UseSwagger();
            app.UseSwaggerUI();
            app.UseStaticFiles();
            app.UseCors();
            //app.UseAuthentication();
            //app.UseAuthorization();
            app.UseErrorHandler();
            app.MapControllers();
            app.Run();
        }
    }
}
