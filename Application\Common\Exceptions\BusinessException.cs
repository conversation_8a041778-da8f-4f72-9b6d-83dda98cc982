﻿namespace Application.Common.Exceptions
{
    public class BusinessException : Exception
    {
        public string errorMessage { get; set; }
        public string errorCode { get; set; }
        public string? entityName { get; set; }
        public int resultCode { get; set; }
        public BusinessException(string message, string? code, string? entityName, int resultCode = -1) : base(message)
        {
            errorMessage = message;
            this.entityName = entityName;

            this.resultCode = resultCode;

            if (code != null)
                errorCode = code;
            else
                errorCode = "general_error";
        }
        public BusinessException() : base()
        {
            errorMessage = "BusinessException";
            errorCode = "general_error";
        }
    }
}
