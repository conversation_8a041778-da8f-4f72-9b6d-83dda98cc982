﻿namespace Presentation.DTO;

public class UploadedFile
{
    public string OriginalFileName { get; set; } = string.Empty;
    public string RelativePath { get; set; } = string.Empty;
    public string? ThumbnailRelativePath =>
        IsVideo ? Path.ChangeExtension(RelativePath, ".jpg") : null;
    public string ContentType { get; set; } = string.Empty;
    public string Extension { get; set; } = string.Empty;
    public long Size { get; set; }
    public bool IsVideo { get; set; }
    public Stream? FileStream { get; set; }
}