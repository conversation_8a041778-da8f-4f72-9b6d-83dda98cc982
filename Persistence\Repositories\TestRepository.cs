﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Application.Repositories;
using Microsoft.EntityFrameworkCore;
using Persistence.Context;

namespace Persistence.Repositories
{
    public class TestRepository : ITestRepository
    {
        private AppDbContext _appDbContext;

        public TestRepository(AppDbContext appDbContext)
        {
            _appDbContext = appDbContext;
        }

        public async Task<bool> CanConnect()
        {
            var canConnect = await _appDbContext.Database.CanConnectAsync();

            return canConnect;
        }
    }
}
