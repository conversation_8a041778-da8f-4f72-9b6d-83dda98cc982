﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="address" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="CorporateUserRequestId" xml:space="preserve">
    <value>معرف طلب مستخدم الشركة</value>
  </data>
  <data name="ProveRequestId" xml:space="preserve">
    <value>معرف طلب الإثبات</value>
  </data>
  <data name="SharedWithUserId" xml:space="preserve">
    <value>رقم المستخدم المراد المشاركة معه</value>
  </data>
  <data name="ProveSharedUserId" xml:space="preserve">
    <value>الرقم التعريفي للإثبات المراد مشاركته</value>
  </data>
  <data name="ProveSharedUserToken" xml:space="preserve">
    <value>الرقم التعريفي للإثبات المراد مشاركته</value>
  </data>
  <data name="ProveId" xml:space="preserve">
    <value>رقم الإثبات</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>البريد الإلكتروني</value>
  </data>
  <data name="city" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="corporateNameAr" xml:space="preserve">
    <value>اسم الشركة باللغة العربية</value>
  </data>
  <data name="corporateNameEn" xml:space="preserve">
    <value>اسم الشركة باللغة الإنجليزية</value>
  </data>
  <data name="CRNumber" xml:space="preserve">
    <value>رقم السجل التجاري</value>
  </data>
  <data name="currentPassword" xml:space="preserve">
    <value>كلمة المرور الحالية</value>
  </data>
  <data name="fullAddress" xml:space="preserve">
    <value>وصف العنوان</value>
  </data>
  <data name="latitude" xml:space="preserve">
    <value>خط الطول من خدمات الموقع</value>
  </data>
  <data name="longitude" xml:space="preserve">
    <value>خط العرض من خدمات الموقع</value>
  </data>
  <data name="mobile" xml:space="preserve">
    <value>رقم الجوال</value>
  </data>
  <data name="name" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="newPassword" xml:space="preserve">
    <value>كلمة المرور الجديدة</value>
  </data>
  <data name="otp" xml:space="preserve">
    <value>رمز التحقق</value>
  </data>
  <data name="ownerName" xml:space="preserve">
    <value>اسم المالك</value>
  </data>
  <data name="password" xml:space="preserve">
    <value>كلمة المرور</value>
  </data>
  <data name="requestId" xml:space="preserve">
    <value>رقم الطلب</value>
  </data>
  <data name="taxNumber" xml:space="preserve">
    <value>الرقم الضريبي</value>
  </data>
  <data name="ProveId1" xml:space="preserve">
    <value>ألبريد الإلكتروني</value>
  </data>
  <data name="ContentType" xml:space="preserve">
    <value>نوع المحتوي</value>
  </data>
  <data name="CorporateId" xml:space="preserve">
    <value>رقم الشركة</value>
  </data>
  <data name="CorporateUserId" xml:space="preserve">
    <value>رقم موظف الشركة</value>
  </data>
  <data name="Extension" xml:space="preserve">
    <value>امتداد</value>
  </data>
  <data name="PageNumber" xml:space="preserve">
    <value>رقم الصفحة</value>
  </data>
  <data name="PageSize" xml:space="preserve">
    <value>حجم الصفحة</value>
  </data>
  <data name="Path" xml:space="preserve">
    <value>مسار الملف</value>
  </data>
  <data name="ProveAttachmentId" xml:space="preserve">
    <value>رقم الملف المرفق</value>
  </data>
  <data name="ProveTypeId" xml:space="preserve">
    <value>رقم نوع الإثبات</value>
  </data>
  <data name="ProveTypeNameAr" xml:space="preserve">
    <value>الاسم العربي لنوع الأثبات</value>
  </data>
  <data name="ProveTypeNameEn" xml:space="preserve">
    <value>الاسم الانجليزي لنوع الأثبات</value>
  </data>
  <data name="Size" xml:space="preserve">
    <value>حجم الملف</value>
  </data>
  <data name="ProfilePictureId" xml:space="preserve">
    <value>رقم صورة الملف الشخصي</value>
  </data>
  <data name="FileStream" xml:space="preserve">
    <value>محتوى المرفق</value>
  </data>
</root>