﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="101" xml:space="preserve">
    <value>Can't create</value>
  </data>
  <data name="201" xml:space="preserve">
    <value>Request Already Sent</value>
  </data>
  <data name="204" xml:space="preserve">
    <value>Request Is Not Found</value>
  </data>
  <data name="205" xml:space="preserve">
    <value>you don't have privilege for this feature</value>
  </data>
  <data name="202" xml:space="preserve">
    <value>Mobile Number Is Not Exist</value>
  </data>
  <data name="203" xml:space="preserve">
    <value>General Exception</value>
  </data>
  <data name="0101" xml:space="preserve">
    <value>Can't activate this address</value>
  </data>
  <data name="0102" xml:space="preserve">
    <value>Can't create this address</value>
  </data>
  <data name="0103" xml:space="preserve">
    <value>Address Not Found</value>
  </data>
  <data name="0104" xml:space="preserve">
    <value>Can't deactivate address</value>
  </data>
  <data name="0105" xml:space="preserve">
    <value>Error while returning addresses</value>
  </data>
  <data name="0106" xml:space="preserve">
    <value>Error while updating address</value>
  </data>
  <data name="1101" xml:space="preserve">
    <value>Prove Not Found</value>
  </data>
  <data name="general_activate_prove" xml:space="preserve">
    <value>General error while activate prove</value>
  </data>
  <data name="general_create_prove" xml:space="preserve">
    <value>General error while create proof</value>
  </data>
  <data name="general_deactivate_prove" xml:space="preserve">
    <value>General error while deactivate prove</value>
  </data>
  <data name="general_delete_prove" xml:space="preserve">
    <value>General error while delete proof</value>
  </data>
  <data name="general_get_paginated_prove" xml:space="preserve">
    <value>General error while returning proves</value>
  </data>
  <data name="general_update_prove" xml:space="preserve">
    <value>General error while update proof</value>
  </data>
  <data name="1301" xml:space="preserve">
    <value>Prove Shared User Is Not Found</value>
  </data>
  <data name="1302" xml:space="preserve">
    <value>Token Is Expired</value>
  </data>
  <data name="address_not_found" xml:space="preserve">
    <value>Selected address not found</value>
  </data>
  <data name="prove_not_found" xml:space="preserve">
    <value>Selected proof not found</value>
  </data>
  <data name="prove_shared_user_not_found" xml:space="preserve">
    <value>Proof shared user not found</value>
  </data>
  <data name="prove_request_not_found" xml:space="preserve">
    <value>Proof request not found</value>
  </data>
  <data name="prove_sharing_expired" xml:space="preserve">
    <value>Proof shared link is expired</value>
  </data>
  <data name="prove_sharing_exceeded_allowed_opens" xml:space="preserve">
    <value>Proof shared link exceeded the number of allowed opens</value>
  </data>
  <data name="corporate_not_found" xml:space="preserve">
    <value>Selected corporate not found</value>
  </data>
  <data name="corporate_user_not_found" xml:space="preserve">
    <value>Selected corporate user not found</value>
  </data>
  <data name="Same_Corporate_User" xml:space="preserve">
    <value>You entered the same corporate user Id</value>
  </data>
  <data name="corporate_user_request_not_found" xml:space="preserve">
    <value>Selected corporate user request not found</value>
  </data>
  <data name="general_activate_prove_type" xml:space="preserve">
    <value>General error while activate prove type</value>
  </data>
  <data name="general_create_prove_attachment" xml:space="preserve">
    <value>General error while create proof attachment</value>
  </data>
  <data name="general_create_prove_type" xml:space="preserve">
    <value>General error while create proof type</value>
  </data>
  <data name="general_deactivate_prove_type" xml:space="preserve">
    <value>General error while deactivate prove type</value>
  </data>
  <data name="general_delete_prove_attachment" xml:space="preserve">
    <value>General error while delete proof attachment</value>
  </data>
  <data name="general_delete_prove_type" xml:space="preserve">
    <value>General error while delete proof type</value>
  </data>
  <data name="general_get_all_prove_types" xml:space="preserve">
    <value>General error while returning proof types</value>
  </data>
  <data name="general_get_prove_by_id" xml:space="preserve">
    <value>General error while return proof</value>
  </data>
  <data name="general_get_prove_type" xml:space="preserve">
    <value>General error while return proof type</value>
  </data>
  <data name="general_update_prove_attachment" xml:space="preserve">
    <value>General error while update proof attachment</value>
  </data>
  <data name="general_update_prove_type" xml:space="preserve">
    <value>General error while update proof type</value>
  </data>
  <data name="prove_attachment_not_found" xml:space="preserve">
    <value>Selected proof attachment not found</value>
  </data>
  <data name="prove_type_not_found" xml:space="preserve">
    <value>Selected proof type not found</value>
  </data>
  <data name="user_not_found" xml:space="preserve">
    <value>Selected user not found </value>
  </data>
  <data name="cannot_share_with_yourself" xml:space="preserve">
    <value>You cannot share with yourself</value>
  </data>
  <data name="cant_share_yourself" xml:space="preserve">
    <value>You can't add your corporate</value>
  </data>
  <data name="offer_type_not_found" xml:space="preserve">
    <value>Selected offer type not found </value>
  </data>
  <data name="failed_payment" xml:space="preserve">
    <value>Failed payment</value>
  </data>
  <data name="corporate_not_subscribed" xml:space="preserve">
    <value>Selected corporate not subscriped </value>
  </data>
  <data name="resend_otp_not_allowed" xml:space="preserve">
    <value>Resend OTP is not allowed Please try again later</value>
  </data>
  <data name="Mail_Exceptions" xml:space="preserve">
    <value>Mail didn't send successfully</value>
  </data>
  <data name="user_already_exists" xml:space="preserve">
    <value>User already exists in the corporate</value>
  </data>
  <data name="request_already_sent" xml:space="preserve">
    <value>Request Already Sent</value>
  </data>
  <data name="Cant_Send_To_Corporate" xml:space="preserve">
    <value>Can't send request to corporate</value>
  </data>
  <data name="invalid_username_or_password" xml:space="preserve">
    <value>The username or password provided is invalid</value>
  </data>
  <data name="request_not_found" xml:space="preserve">
    <value>The request could not be found</value>
  </data>
  <data name="token_already_used" xml:space="preserve">
    <value>The token has already been used</value>
  </data>
  <data name="invalid_otp" xml:space="preserve">
    <value>The provided OTP is invalid</value>
  </data>
  <data name="invalid_token" xml:space="preserve">
    <value>The provided token is invalid</value>
  </data>
  <data name="user_already_registered" xml:space="preserve">
    <value>The user is already registered</value>
  </data>
  <data name="request_not_verified" xml:space="preserve">
    <value>The request has not been verified</value>
  </data>
  <data name="city_not_found" xml:space="preserve">
    <value>Selected city not found</value>
  </data>
  <data name="general_activate_address" xml:space="preserve">
    <value>General error while activate address</value>
  </data>
  <data name="general_activate_city" xml:space="preserve">
    <value>General error while activate city</value>
  </data>
  <data name="general_create_address" xml:space="preserve">
    <value>General error while create address</value>
  </data>
  <data name="general_create_city" xml:space="preserve">
    <value>General error while create city</value>
  </data>
  <data name="general_deactivate_address" xml:space="preserve">
    <value>General error while deactivate address</value>
  </data>
  <data name="general_deactivate_city" xml:space="preserve">
    <value>General error while deactivate city</value>
  </data>
  <data name="general_delete_address" xml:space="preserve">
    <value>General error while delete address</value>
  </data>
  <data name="general_delete_city" xml:space="preserve">
    <value>General error while delete city</value>
  </data>
  <data name="general_get_address" xml:space="preserve">
    <value>General error while return address</value>
  </data>
  <data name="general_get_all_cities" xml:space="preserve">
    <value>General error while return cities</value>
  </data>
  <data name="general_get_city" xml:space="preserve">
    <value>General error while return city</value>
  </data>
  <data name="general_get_paginated_address" xml:space="preserve">
    <value>General error while return addresses</value>
  </data>
  <data name="general_update_address" xml:space="preserve">
    <value>General error while update address</value>
  </data>
  <data name="general_update_city" xml:space="preserve">
    <value>General error while update city</value>
  </data>
  <data name="corporate_prove_not_same" xml:space="preserve">
    <value>Selected corporate not related to this prove</value>
  </data>
  <data name="not_authorized_user" xml:space="preserve">
    <value>User doesn't have permission to access this proof</value>
  </data>
  <data name="invalid_current_password" xml:space="preserve">
    <value>Invalid Current Password</value>
  </data>
  <data name="general_unhandled_exception" xml:space="preserve">
    <value>Unhandled exception occurred while trying to process your request, please try again later</value>
  </data>
  <data name="Request_Not_Found_accept" xml:space="preserve">
    <value>Request Not Found</value>
  </data>
  <data name="Privilege_cancel" xml:space="preserve">
    <value>You don't have privilege to this feature</value>
  </data>
  <data name="Cannot_cancel" xml:space="preserve">
    <value>You can't cancel this proof request</value>
  </data>
  <data name="Cannot_Complete" xml:space="preserve">
    <value>You can't complete this proof request</value>
  </data>
  <data name="Cannot_delete" xml:space="preserve">
    <value>You can't delete this proof request</value>
  </data>
  <data name="general_accept" xml:space="preserve">
    <value>Generl error while accepting request</value>
  </data>
  <data name="general_Cancel" xml:space="preserve">
    <value>General error while Cancelling request</value>
  </data>
  <data name="unauthorized_deletion" xml:space="preserve">
    <value>You don't have permission to delete user</value>
  </data>
  <data name="prove_already_shared_with_selected_user" xml:space="preserve">
    <value>Proof is currently shared with the selected user</value>
  </data>
  <data name="prove_folder_already_shared_with_selected_user" xml:space="preserve">
    <value>Proof folder is currently shared with the selected user</value>
  </data>
  <data name="general_update_user" xml:space="preserve">
    <value>General error while update user</value>
  </data>
  <data name="profile_picture_not_found" xml:space="preserve">
    <value>Profile picture not found</value>
  </data>
  <data name="plan_not_found" xml:space="preserve">
    <value>Selected plan not found</value>
  </data>
  <data name="offer_not_found" xml:space="preserve">
    <value>Selected offer not found </value>
  </data>
  <data name="prove_request_not_authorized" xml:space="preserve">
    <value>You don't have permission to complete the selected proof request.</value>
  </data>
  <data name="force_update_msg" xml:space="preserve">
    <value>To enjoy Athbit latest features, please update the application to latest version</value>
  </data>
  <data name="cant-connect" xml:space="preserve">
    <value>Database connection failed.</value>
  </data>
</root>