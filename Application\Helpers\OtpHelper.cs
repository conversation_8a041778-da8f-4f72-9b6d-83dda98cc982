﻿using System.Text;

namespace Application.Helpers;

public static class OtpHelper
{
    public static string GenerateOtp(int length = 4)
    {
        const string allowedChars = "0123456789";
        StringBuilder otp = new StringBuilder();

        Random random = new Random();
        for (int i = 0; i < length; i++)
        {
            int index = random.Next(0, allowedChars.Length);
            otp.Append(allowedChars[index]);
        }

        return otp.ToString();
    }

    public static bool VerifyOtp(string enteredOtp, string expectedOtp, DateTimeOffset? expireDate)
    {
        //bool isOTPValid = string.Equals(enteredOtp, expectedOtp, StringComparison.Ordinal);

        //bool isNotExpired = expireDate is not null && DateTime.Now <= expireDate;

        //return isOTPValid && isNotExpired;

        // Fake OTP verification for testing
        return true;
    }
}
