﻿using Azure.Core;
using System.Threading;
using Google;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Persistence.Context;
using Application.Features.GetTestFeature;

namespace Presentation.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TestController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<TestController> _logger;
        private readonly AppDbContext _context;

        public TestController(IMediator mediator, ILogger<TestController> logger, AppDbContext context)
        {
            _mediator = mediator;
            _logger = logger;
            _context = context;
        }


        [HttpGet]
        [Route("test-db-connection")]
        public async Task<ActionResult<GetTestFeatureResponse>> GetTest([FromQuery] GetTestFeatureRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var response = await _mediator.Send(request, cancellationToken);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);

                throw ex;
            }
        }
    }
}