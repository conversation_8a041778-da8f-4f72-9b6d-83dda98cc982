﻿using MediatR.NotificationPublishers;

namespace Application.Common;

public static class Constants
{
    public const int CommonPageNumber = 1;
    public const int CommonPageSize = 50;
    public const int SmsArabicType = 4;
    public const int SmsEnglishType = 1;
    public const string Mongo_Collection_Name = "AthbitNotificationHistory";
    public const string Preview_Not_Found_Image_Path = "defaults/NoAttachment.png";
}

public class CustomClaimTypes
{
    public const string CorporateId = "CorporateId";
    public const string UserId = "UserId";
}

public static class NotificationKeys
{
    public const string SEND_EMP_CORPORATE_REQUEST = "send_emp_corporate_request";
    public const string APPROVE_EMP_CORPORATE_REQUEST = "approve_emp_corporate_request";
    public const string REJECT_EMP_CORPORATE_REQUEST = "reject_emp_corporate_request";
    public const string DELETE_EMP_CORPORATE_REQUEST = "delete_emp_corporate_request";

    public const string SHARE_PROVE = "share_prove";
    public const string SHARE_FOLDER = "share_folder";

    public const string CREATE_PROVE_REQUEST = "create_prove_request";
    public const string CHANGE_PROVE_REQUEST = "change_prove_request";
    public const string DELETE_PROVE_REQUEST = "delete_prove_request";
    public const string COMPLETE_PROVE_REQUEST = "complete_prove_request";
}