﻿using Application.Common.Exceptions;
using Application.Common.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Presentation.Resources;

namespace Presentation.Helpers
{
    public class ResourceErrorFilter : ExceptionFilterAttribute
    {
        public MessageResourceContext _errorMessageResourceContext;
        public MessageResourceContext _validationMessageResourceContext;
        public MessageResourceContext _validationMembersResourceContext;

        public ResourceErrorFilter(
            MessageResourceContext errorMessageResourceContext,
            MessageResourceContext validationMessagesResourceType,
            MessageResourceContext validationMembersResourceType
            )
        {
            _errorMessageResourceContext = errorMessageResourceContext;
            _validationMessageResourceContext = validationMessagesResourceType;
            _validationMembersResourceContext = validationMembersResourceType;
        }

        public override void OnException(ExceptionContext context)
        {

            if (!context.ExceptionHandled)
            {
                if (context.Exception is BusinessException businessException)
                {
                    string resourceKey = businessException.errorCode;
                    string errorMessage = _errorMessageResourceContext.GetMessage(resourceKey.ToString());

                    GeneralResponse returnPattern = new GeneralResponse()
                    {
                        ErrorMessage = errorMessage,
                        ResultCode = businessException.resultCode,
                        SuccessMessage = ""
                    };
                    context.Result = new ObjectResult(returnPattern) { StatusCode = 500 };

                    context.ExceptionHandled = true;

                }
                else if (context.Exception is ValidationException validationException)
                {
                    string responseMessage = "";
                    foreach (string code in validationException.errorCodes)
                    {
                        string[] codes = code.Split('-');
                        string memberName = codes[codes.Length - 1];
                        for (int i = 0; i < codes.Length - 1; i++)
                        {
                            responseMessage += string.Format(
                                _validationMessageResourceContext.GetMessage(codes[i]),
                                _validationMembersResourceContext.GetMessage(memberName));
                            responseMessage += "@";
                        }
                    }
                    GeneralResponse returnPattern = new GeneralResponse()
                    {
                        ErrorMessage = responseMessage.Replace("@", Environment.NewLine),
                        ResultCode = -1,
                        SuccessMessage = ""
                    };
                    context.Result = new ObjectResult(returnPattern) { StatusCode = 400 };

                    context.ExceptionHandled = true;
                }
                else if (context.Exception is UnauthorizedAccessException unauthorizedAccessException)
                {
                    GeneralResponse returnPattern = new GeneralResponse()
                    {
                        ErrorMessage = "Unauthorized Access",
                        ResultCode = -1,
                        SuccessMessage = ""
                    };
                    context.Result = new ObjectResult(returnPattern) { StatusCode = 401 };

                    context.ExceptionHandled = true;
                }
                else
                {
                    string errorMessage = _errorMessageResourceContext.GetMessage("general_unhandled_exception");

                    GeneralResponse returnPattern = new GeneralResponse()
                    {
                        ErrorMessage = errorMessage,
                        ResultCode = -1,
                        SuccessMessage = ""
                    };
                    context.Result = new ObjectResult(returnPattern) { StatusCode = 500 };

                    context.ExceptionHandled = true;
                }

            }
        }
    }
}
