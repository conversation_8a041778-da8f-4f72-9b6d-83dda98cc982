﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using Application.Common.Exceptions;
using Application.Common.Repositories;
using Application.Repositories;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Features.GetTestFeature
{
    public class GetTestFeatureHandler : IRequestHandler<GetTestFeatureRequest, GetTestFeatureResponse>
    {
        private readonly ITestRepository _repo;

        public GetTestFeatureHandler(ITestRepository repo)
        {
            _repo = repo;
        }

        public async Task<GetTestFeatureResponse> Handle(GetTestFeatureRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var canConnect = await _repo.CanConnect();
                if (!canConnect) 
                {
                    throw new GeneralException()
                    {
                        InnerMessage = "cant-connect",
                        ResultCode = -2
                    };
                }

                return new GetTestFeatureResponse { Result = "Database connection successful." };
            }
            catch (GeneralException ex)
            {
                throw new BusinessException(ex.Message, ex.InnerMessage, "Test", ex.ResultCode != null ? ex.ResultCode.Value : -1);
            }
            catch (Exception exception)
            {
                string exceptionMessage = string.Format(GetType().Name + "\n" + "message: " + exception.Message + " \n" + "inner exception: " + Convert.ToString(exception.InnerException) + " \n");
                throw new BusinessException(exceptionMessage, "general_create_city", "Test");
            }
        }

    }
}
