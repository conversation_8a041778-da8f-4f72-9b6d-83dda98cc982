﻿using Domain.Entities;
using FFMpegCore;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats;
using SixLabors.ImageSharp.Formats.Bmp;
using SixLabors.ImageSharp.Formats.Gif;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Tiff;
using SixLabors.ImageSharp.Processing;
using System.Diagnostics;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using Xabe.FFmpeg;

namespace Application.Common
{
    public static class SharedFunctions
    {
        public static void CreateCorporateFolder(Guid corporateId)
        {
            string newFolderPath = filePathForCorp(corporateId);
            if (!Directory.Exists(newFolderPath))
            {
                Directory.CreateDirectory(newFolderPath);
            }
        }

        public static string filePathForCorp(Guid corporateId)
        {
            string baseFolderPath = Path.Combine(Directory.GetCurrentDirectory(), "Corporates");
            baseFolderPath = Path.GetFullPath(baseFolderPath);
            string uniqueFolderName = GenerateCorporateUniqueName(corporateId);
            string newFolderPath = Path.Combine(baseFolderPath, uniqueFolderName);
            return newFolderPath;
        }

        public static string GenerateCorporateUniqueName(Guid corporateId)
        {
            string hashedGuid = HashGuid(corporateId.ToString());
            string uniqueFolderName = $"corp_{hashedGuid}";
            return uniqueFolderName;
        }

        public static string getDefaultImagePath()
        {
            string baseFolderPath = Path.Combine(Directory.GetCurrentDirectory(), "Corporates", "c8aec738-3448-4302-b73a-9ccad7ff4e13.jpeg");
            return Path.GetFullPath(baseFolderPath);
        }

        private static string HashGuid(string guid)
        {
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(guid));

                StringBuilder hashString = new StringBuilder();
                foreach (byte b in hashBytes)
                {
                    hashString.Append(b.ToString("x2"));
                }

                return hashString.ToString().Substring(0, 16);
            }
        }

        public static string GenerateThumbnail(string filePath, TimeSpan captureTime)
        {
            var outputThumbnailPath = Path.Combine(Directory.GetCurrentDirectory(), filePath.Split(".")[0] + ".png");
            try
            {
                FFMpeg.Snapshot(filePath, outputThumbnailPath, new System.Drawing.Size(500, 500), captureTime);
            }
            catch (Exception)
            {
                throw;
            }

            return outputThumbnailPath;
        }
        public static async Task<byte[]> GenerateThumbnailFromStream(Stream videoStream)
        {
            Console.WriteLine($"Original videoStream.CanSeek: {videoStream.CanSeek}");
            Console.WriteLine($"Position: {videoStream.Position}");
            Console.WriteLine($"Length: {videoStream.Length}");
            
            var startInfo = new ProcessStartInfo
            {
                FileName = "ffmpeg",
                Arguments = "-i pipe:0 -ss 00:00:01 -vframes 1 -vf scale=320:-1 -f mjpeg pipe:1",
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var ffmpeg = new Process { StartInfo = startInfo };
            ffmpeg.Start();

            // Pipe the seekable stream to FFmpeg
            var stdinTask = Task.Run(async () =>
            {
                await videoStream.CopyToAsync(ffmpeg.StandardInput.BaseStream);
                ffmpeg.StandardInput.Close();
            });

            // Capture error output
            //string errorOutput = await ffmpeg.StandardError.ReadToEndAsync();

            // Read the output (thumbnail)
            using var ms = new MemoryStream();
            await ffmpeg.StandardOutput.BaseStream.CopyToAsync(ms);
            await ffmpeg.WaitForExitAsync();

            //if (ms.Length == 0)
            //{
            //    Console.WriteLine($"FFmpeg error: {errorOutput}");
            //}

            return ms.ToArray();
        }
        public static async Task<Stream> ResizeImageStreamAsync(Stream inputStream)
        {
            int targetWidth = 320;
            using var image = await Image.LoadAsync(inputStream);

            // Maintain aspect ratio
            var targetHeight = (int)((double)targetWidth / image.Width * image.Height);
            image.Mutate(x => x.Resize(targetWidth, targetHeight));

            var outputStream = new MemoryStream();
            await image.SaveAsJpegAsync(outputStream);

            outputStream.Position = 0; // Important: rewind so it’s ready for reading
            return outputStream;
        }
        public static string GenerateRandomTokenString()
        {
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            var stringChars = new char[64];
            var random = new Random();

            for (int i = 0; i < stringChars.Length; i++)
            {
                stringChars[i] = chars[random.Next(chars.Length)];
            }
            return new String(stringChars);
        }

        public static string ComputeSha256Hash(string rawData)
        {
            // Create a SHA256
            using (SHA256 sha256Hash = SHA256.Create())
            {
                // ComputeHash - returns byte array
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(rawData));

                // Convert byte array to a string
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        public static string GenerateResetPasswordEmailBody(string name, string link)
        {
            string template = "أهلاً " + name + ",\r\nلديك طلب تغيير كلمة المرور الخاصة بك!\r\n\r\nإذا لم تقم بتقديم هذا الطلب، يرجى تجاهل هذا البريد الإلكتروني.\r\n\r\nبخلاف ذلك، يرجى النقر على هذا الرابط لتغيير كلمة المرور الخاصة بك" + link + "\r\n\r\n";
            template += "Hi " + name + ",\r\nThere was a request to change your password!\r\n\r\nIf you did not make this request then please ignore this email.\r\n\r\nOtherwise, please click this link to change your password: " + link + "\r\n\r\n";
            return template;
        }

        public static string GenerateShareProveEmailBody(string shareFromName, string link)
        {
            string template = "أهلاً ,\r\nتم مشاركة اثبات معك عبر تطبيق أثبت \r\n\r\n تم مشاركته معك بواسطة: " + shareFromName + "\r\n\r\n" + "انقر هنا لاستعراض بيانات الاثبات:" + link + "\r\n\r\n";
            template += "Hi ,\r\nProof has been shared with you via the أثبت app.\r\n\r\nShared by:" + shareFromName + "\r\n\r\n" + "please click this link to view proof data:" + link + "\r\n\r\n";
            return template;
        }

        public static string GenerateShareProveFolderEmailBody(string shareFromName, string link)
        {
            string template = "أهلاً ,\r\nتم مشاركة ملف إثباتات معك عبر تطبيق أثبت \r\n\r\n تم مشاركته معك بواسطة: " + shareFromName + "\r\n\r\n" + "انقر هنا لاستعراض بيانات الملف:" + link + "\r\n\r\n";
            template += "Hi ,\r\nProofs folder has been shared with you via the أثبت app.\r\n\r\nShared by:" + shareFromName + "\r\n\r\n" + "please click this link to view proof folder data:" + link + "\r\n\r\n";
            return template;
        }

        public static bool SendEmail(string EmailAddress, string Title, string Body)
        {
            MailAddress from = new MailAddress("<EMAIL>");
            using (SmtpClient client = new SmtpClient("mail.alemtithal.com", 25) { DeliveryMethod = SmtpDeliveryMethod.Network, EnableSsl = false, UseDefaultCredentials = false })
            {
                client.Credentials = new System.Net.NetworkCredential("<EMAIL>", "elemt!th@l");
                MailAddress to = new MailAddress(EmailAddress);
                try
                {
                    using (MailMessage message = new MailMessage(from, to) { })
                    {
                        message.IsBodyHtml = true;
                        message.Body = Body;
                        message.SubjectEncoding = Encoding.Unicode;
                        message.Subject = Title;
                        message.Headers.Add("Content-Type", "text/plain;charset=Unicode");
                        client.Send(message);
                    }
                }
                catch (Exception)
                {
                    throw;
                }
                return true;
            }
        }
    }
}
