﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Presentation.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ValidationMembers {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ValidationMembers() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Presentation.Resources.ValidationMembers", typeof(ValidationMembers).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        public static string address {
            get {
                return ResourceManager.GetString("address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        public static string city {
            get {
                return ResourceManager.GetString("city", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Corporate name in arabic.
        /// </summary>
        public static string corporateNameAr {
            get {
                return ResourceManager.GetString("corporateNameAr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Corporate name in english.
        /// </summary>
        public static string corporateNameEn {
            get {
                return ResourceManager.GetString("corporateNameEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Corporate User Request Id.
        /// </summary>
        public static string CorporateUserRequestId {
            get {
                return ResourceManager.GetString("CorporateUserRequestId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commercial registeration number.
        /// </summary>
        public static string CRNumber {
            get {
                return ResourceManager.GetString("CRNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current password.
        /// </summary>
        public static string currentPassword {
            get {
                return ResourceManager.GetString("currentPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string email {
            get {
                return ResourceManager.GetString("email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address description.
        /// </summary>
        public static string fullAddress {
            get {
                return ResourceManager.GetString("fullAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location latitude.
        /// </summary>
        public static string latitude {
            get {
                return ResourceManager.GetString("latitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location longitude.
        /// </summary>
        public static string longitude {
            get {
                return ResourceManager.GetString("longitude", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile number.
        /// </summary>
        public static string mobile {
            get {
                return ResourceManager.GetString("mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string name {
            get {
                return ResourceManager.GetString("name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password.
        /// </summary>
        public static string newPassword {
            get {
                return ResourceManager.GetString("newPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One time password.
        /// </summary>
        public static string otp {
            get {
                return ResourceManager.GetString("otp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Owner name.
        /// </summary>
        public static string ownerName {
            get {
                return ResourceManager.GetString("ownerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        public static string password {
            get {
                return ResourceManager.GetString("password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prove Id.
        /// </summary>
        public static string ProveId {
            get {
                return ResourceManager.GetString("ProveId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        public static string ProveId1 {
            get {
                return ResourceManager.GetString("ProveId1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prove Shared User Id.
        /// </summary>
        public static string ProveSharedUserId {
            get {
                return ResourceManager.GetString("ProveSharedUserId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request number.
        /// </summary>
        public static string requestId {
            get {
                return ResourceManager.GetString("requestId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shared With User Id.
        /// </summary>
        public static string SharedWithUserId {
            get {
                return ResourceManager.GetString("SharedWithUserId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax number.
        /// </summary>
        public static string taxNumber {
            get {
                return ResourceManager.GetString("taxNumber", resourceCulture);
            }
        }
    }
}
