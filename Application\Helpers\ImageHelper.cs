﻿using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats;
using SixLabors.ImageSharp.Formats.Bmp;
using SixLabors.ImageSharp.Formats.Gif;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using SixLabors.ImageSharp.Formats.Tiff;
using SixLabors.ImageSharp.Processing;

namespace Application.Helpers;

public static class ImageHelper
{
    public static async Task<string> CompressImageForMobileAsync(string inputPath, string format, int mobileWidth = 720, int jpegQuality = 75)
    {
        // Load the image from file
        using var image = await Image.LoadAsync(inputPath);

        // Resize to mobile-friendly width while maintaining aspect ratio
        image.Mutate(x => x.Resize(new ResizeOptions
        {
            Mode = ResizeMode.Max,
            Size = new Size(mobileWidth, 0) // Set only width to keep aspect ratio
        }));

        // Choose encoder based on the format
        IImageEncoder encoder = format.ToLower() switch
        {
            "jpeg" or "jpg" => new JpegEncoder { Quality = jpegQuality }, // jpegQuality ranges from 0 to 100 in percentage %
            "png" => new PngEncoder { CompressionLevel = PngCompressionLevel.BestCompression },
            "gif" => new GifEncoder(),
            "bmp" => new BmpEncoder(),
            "tiff" or "tif" => new TiffEncoder(),
            _ => throw new NotSupportedException("Only JPEG, PNG, GIF, BMP, and TIFF formats are supported.")
        };

        // Save the compressed image to a MemoryStream
        using var memoryStream = new MemoryStream();
        await image.SaveAsync(memoryStream, encoder);

        // Convert the MemoryStream to a Base64 string
        byte[] imageBytes = memoryStream.ToArray();
        return Convert.ToBase64String(imageBytes);
    }
}